import { 
  Car, 
  CreditCard, 
  DollarSign, 
  <PERSON>ch, 
  Calendar,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  LogIn
} from "lucide-react";
import { StatsCard } from "@/components/dashboard/StatsCard";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { RecentActivity } from "@/components/dashboard/RecentActivity";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

export default function Dashboard() {
  const navigate = useNavigate();

  return (
    <div className="flex-1 space-y-6 p-6" dir="rtl">
      <div className="w-full max-w-none space-y-6 md:space-y-8">
        {/* Header */}
        <div className="fade-in-up">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-3xl font-almarai font-bold text-foreground">
              مرحباً، أحمد محمد
            </h1>
            <div className="flex items-center gap-4">
              <Button
                onClick={() => navigate('/auth')}
                variant="outline"
                size="sm"
                className="text-primary border-primary hover:bg-primary/10"
              >
                <LogIn className="w-4 h-4 mr-2" />
                تسجيل الدخول
              </Button>
              <div className="text-sm text-muted-foreground">
                اليوم: {new Date().toLocaleDateString('ar-SA')}
              </div>
            </div>
          </div>
          <p className="text-muted-foreground font-arabic">
            إليك نظرة عامة على خدمات السيارات والصيانة
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6">
          <div className="fade-in-up" style={{ animationDelay: "0.1s" }}>
            <StatsCard
              title="عدد المركبات"
              value="3"
              icon={Car}
              change="+1 هذا الشهر"
              changeType="positive"
              gradient="blue"
            />
          </div>
          
          <div className="fade-in-up" style={{ animationDelay: "0.2s" }}>
            <StatsCard
              title="إجمالي الدفعات"
              value="12,450 ريال"
              icon={CreditCard}
              change="+15% من الشهر الماضي"
              changeType="positive"
              gradient="green"
            />
          </div>
          
          <div className="fade-in-up" style={{ animationDelay: "0.3s" }}>
            <StatsCard
              title="الرصيد المتبقي"
              value="2,350 ريال"
              icon={DollarSign}
              change="-8% من الشهر الماضي"
              changeType="negative"
              gradient="orange"
            />
          </div>
          
          <div className="fade-in-up" style={{ animationDelay: "0.4s" }}>
            <StatsCard
              title="المواعيد القادمة"
              value="2"
              icon={Calendar}
              change="خلال الأسبوع القادم"
              changeType="neutral"
              gradient="purple"
            />
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Left Column - Charts & Analytics */}
          <div className="lg:col-span-2 space-y-6">
            {/* Quick Actions */}
            <div className="fade-in-up" style={{ animationDelay: "0.5s" }}>
              <QuickActions />
            </div>

            {/* Analytics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
              <div className="fade-in-up" style={{ animationDelay: "0.6s" }}>
                <Card className="premium-card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-almarai font-bold text-card-foreground">
                      إحصائيات الصيانة
                    </h3>
                    <BarChart3 className="w-5 h-5 text-primary" />
                  </div>
                  
                  {/* Simple chart placeholder */}
                  <div className="h-32 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-lg flex items-end justify-center gap-2 p-4">
                    <div className="w-8 h-16 bg-primary/30 rounded-t"></div>
                    <div className="w-8 h-20 bg-primary/50 rounded-t"></div>
                    <div className="w-8 h-12 bg-primary/40 rounded-t"></div>
                    <div className="w-8 h-24 bg-primary/60 rounded-t"></div>
                    <div className="w-8 h-18 bg-primary/35 rounded-t"></div>
                  </div>
                  
                  <div className="mt-4 text-center">
                    <p className="text-sm text-muted-foreground font-arabic">
                      إجمالي الصيانات هذا الشهر: 8
                    </p>
                  </div>
                </Card>
              </div>

              <div className="fade-in-up" style={{ animationDelay: "0.7s" }}>
                <Card className="premium-card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-almarai font-bold text-card-foreground">
                      توزيع المصروفات
                    </h3>
                    <PieChart className="w-5 h-5 text-primary" />
                  </div>
                  
                  {/* Simple pie chart placeholder */}
                  <div className="h-32 flex items-center justify-center">
                    <div className="w-24 h-24 rounded-full bg-gradient-to-r from-primary via-primary-light to-primary-dark relative">
                      <div className="absolute inset-2 bg-background rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-primary">78%</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">صيانة دورية</span>
                      <span className="font-medium">45%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">إصلاحات</span>
                      <span className="font-medium">33%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">قطع غيار</span>
                      <span className="font-medium">22%</span>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>

          {/* Right Column - Recent Activity */}
          <div className="fade-in-up" style={{ animationDelay: "0.8s" }}>
            <RecentActivity />
          </div>
        </div>

        {/* Latest Status Card */}
        <div className="fade-in-up" style={{ animationDelay: "0.9s" }}>
          <Card className="premium-card p-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500/20 to-green-600/5 rounded-xl flex items-center justify-center">
                <Wrench className="w-6 h-6 text-green-600" />
              </div>
              
              <div className="flex-1">
                <h3 className="font-almarai font-bold text-card-foreground mb-2">
                  آخر حالة إصلاح
                </h3>
                <p className="text-muted-foreground font-arabic mb-4">
                  تم إنجاز الصيانة الدورية لسيارة تويوتا كامري (أ ب ج 1234) بنجاح.
                  تم تغيير الزيت والفلاتر وفحص الفرامل.
                </p>
                
                <div className="flex items-center gap-4 text-sm">
                  <span className="text-green-600 font-medium">مكتملة</span>
                  <span className="text-muted-foreground">التكلفة: 850 ريال</span>
                  <span className="text-muted-foreground">التاريخ: 15 نوفمبر 2024</span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}