import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { 
  Wrench, 
  Calendar, 
  Upload, 
  CreditCard,
  ArrowLeft 
} from "lucide-react";

const quickActions = [
  {
    title: "حجز موعد صيانة",
    description: "احجز موعداً جديداً لصيانة مركبتك",
    icon: Calendar,
    action: "/appointments/new",
    color: "blue",
  },
  {
    title: "رفع صور الأضرار",
    description: "ارفع صور الأضرار للحصول على تقدير",
    icon: Upload,
    action: "/upload",
    color: "green",
  },
  {
    title: "عرض الفواتير",
    description: "راجع فواتيرك ومدفوعاتك",
    icon: CreditCard,
    action: "/billing",
    color: "orange",
  },
  {
    title: "البحث عن كراجات",
    description: "ابحث عن أفضل الكراجات القريبة",
    icon: Wrench,
    action: "/garages",
    color: "purple",
  },
];

const colorClasses = {
  blue: "hover:bg-blue-50 dark:hover:bg-blue-950/20 border-blue-200 dark:border-blue-800",
  green: "hover:bg-green-50 dark:hover:bg-green-950/20 border-green-200 dark:border-green-800",
  orange: "hover:bg-orange-50 dark:hover:bg-orange-950/20 border-orange-200 dark:border-orange-800",
  purple: "hover:bg-purple-50 dark:hover:bg-purple-950/20 border-purple-200 dark:border-purple-800",
};

const iconColorClasses = {
  blue: "text-blue-600 bg-blue-100 dark:bg-blue-900/30",
  green: "text-green-600 bg-green-100 dark:bg-green-900/30",
  orange: "text-orange-600 bg-orange-100 dark:bg-orange-900/30",
  purple: "text-purple-600 bg-purple-100 dark:bg-purple-900/30",
};

export function QuickActions() {
  const router = useRouter();

  const handleActionClick = (action: string) => {
    router.push(action);
  };

  return (
    <Card className="premium-card p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-almarai font-bold text-card-foreground">
          إجراءات سريعة
        </h2>
        <Button variant="ghost" size="sm" className="text-primary">
          عرض الكل
          <ArrowLeft className="w-4 h-4 mr-2" />
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {quickActions.map((action, index) => (
          <div
            key={action.title}
            onClick={() => handleActionClick(action.action)}
            className={`
              group p-4 rounded-xl border transition-all duration-300 cursor-pointer
              ${colorClasses[action.color as keyof typeof colorClasses]}
              hover:shadow-md hover:scale-[1.02]
            `}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="flex items-start gap-4">
              <div className={`
                w-12 h-12 rounded-xl flex items-center justify-center
                ${iconColorClasses[action.color as keyof typeof iconColorClasses]}
                group-hover:scale-110 transition-transform duration-300
              `}>
                <action.icon className="w-6 h-6" />
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="font-arabic font-semibold text-card-foreground mb-1">
                  {action.title}
                </h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {action.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}