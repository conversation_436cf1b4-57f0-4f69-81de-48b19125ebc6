import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  Car, 
  Plus, 
  Eye, 
  Edit, 
  Trash2, 
  Settings,
  Search,
  Filter,
  Grid,
  List,
  MoreVertical
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AddVehicleModal } from "@/components/modals/AddVehicleModal";
import { EditVehicleModal } from "@/components/modals/EditVehicleModal";
import { DeleteConfirmModal } from "@/components/modals/DeleteConfirmModal";
import { VehicleSettingsModal } from "@/components/modals/VehicleSettingsModal";

const vehicles = [
  {
    id: 1,
    name: "سيارتي الأساسية",
    model: "تويوتا كامري 2022",
    chassisNumber: "JTDKN3DU8E0123456",
    licensePlate: "أ ب ج 1234",
    status: "جاهزة",
    statusType: "ready" as const,
    licenseExpiry: "2025-03-15",
    image: "/api/placeholder/300/200",
    color: "أبيض لؤلؤي",
    mileage: "45,000 كم",
    lastService: "2024-10-15",
    nextService: "2024-12-15"
  },
  {
    id: 2,
    name: "سيارة العائلة",
    model: "هوندا أكورد 2021",
    chassisNumber: "1HGCV1F30LA123456",
    licensePlate: "د هـ و 5678",
    status: "قيد الصيانة",
    statusType: "maintenance" as const,
    licenseExpiry: "2024-12-20",
    image: "/api/placeholder/300/200",
    color: "أزرق معدني",
    mileage: "62,000 كم",
    lastService: "2024-11-01",
    nextService: "2025-01-01"
  },
  {
    id: 3,
    name: "سيارة العمل",
    model: "نيسان ألتيما 2020",
    chassisNumber: "1N4AL3AP8LC123456",
    licensePlate: "ز ح ط 9012",
    status: "بانتظار قطع الغيار",
    statusType: "pending" as const,
    licenseExpiry: "2024-11-30",
    image: "/api/placeholder/300/200",
    color: "رمادي",
    mileage: "78,000 كم",
    lastService: "2024-09-20",
    nextService: "2024-11-20"
  }
];

const statusColors = {
  ready: "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400",
  maintenance: "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400",
  pending: "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400",
};

export default function Vehicles() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);

  const filteredVehicles = vehicles.filter(vehicle =>
    vehicle.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    vehicle.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
    vehicle.licensePlate.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-background mobile-app-layout mobile-safe-area">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent p-4 md:p-6 border-b border-border/50">
        <div className="w-full px-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="fade-in-up">
              <h1 className="text-3xl font-almarai font-bold text-foreground mb-2">
                مركباتي
              </h1>
              <p className="text-muted-foreground font-arabic">
                إدارة وتتبع جميع مركباتك في مكان واحد
              </p>
            </div>
            
            <AddVehicleModal>
              <Button 
                size="lg" 
                className="btn-premium text-white font-arabic gap-2 w-full sm:w-auto"
              >
                <Plus className="w-5 h-5" />
                إضافة مركبة جديدة
              </Button>
            </AddVehicleModal>
          </div>
        </div>
      </div>

      <div className="w-full p-6 space-y-6">
        {/* Search and Filters */}
        <Card className="premium-card p-6 fade-in-up" style={{ animationDelay: "0.1s" }}>
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <Input
                placeholder="ابحث عن مركبة..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-premium pr-12 h-12 font-arabic"
              />
            </div>

            {/* Controls */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="h-12 px-6"
              >
                <Filter className="w-5 h-5 ml-2" />
                تصفية
              </Button>

              <div className="flex border border-border rounded-lg p-1">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="h-10 px-3"
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="h-10 px-3"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-border/50">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{vehicles.length}</div>
              <div className="text-sm text-muted-foreground font-arabic">إجمالي المركبات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {vehicles.filter(v => v.statusType === "ready").length}
              </div>
              <div className="text-sm text-muted-foreground font-arabic">جاهزة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {vehicles.filter(v => v.statusType === "maintenance").length}
              </div>
              <div className="text-sm text-muted-foreground font-arabic">قيد الصيانة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {vehicles.filter(v => v.statusType === "pending").length}
              </div>
              <div className="text-sm text-muted-foreground font-arabic">معلقة</div>
            </div>
          </div>
        </Card>

        {/* Vehicles Grid/List */}
        <div className={`
          grid gap-6
          ${viewMode === "grid" 
            ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" 
            : "grid-cols-1"
          }
        `}>
          {filteredVehicles.map((vehicle, index) => (
            <Card
              key={vehicle.id}
              className={`
                premium-card overflow-hidden group hover:shadow-xl transition-all duration-300
                ${viewMode === "list" ? "flex flex-col md:flex-row" : ""}
                fade-in-up
              `}
              style={{ animationDelay: `${(index + 2) * 0.1}s` }}
            >
              {/* Image */}
              <div className={`
                relative overflow-hidden
                ${viewMode === "list" ? "md:w-80 h-48 md:h-auto" : "h-48"}
              `}>
                <img
                  src={vehicle.image}
                  alt={vehicle.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-3 left-3">
                  <Badge className={statusColors[vehicle.statusType]}>
                    {vehicle.status}
                  </Badge>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
              </div>

              {/* Content */}
              <div className="p-6 flex-1">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-almarai font-bold text-xl text-card-foreground mb-1">
                      {vehicle.name}
                    </h3>
                    <p className="text-muted-foreground font-arabic">
                      {vehicle.model}
                    </p>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem 
                        onClick={() => router.push(`/vehicles/${vehicle.id}`)}
                        className="font-arabic"
                      >
                        <Eye className="w-4 h-4 ml-2" />
                        عرض التفاصيل
                      </DropdownMenuItem>
                      <EditVehicleModal vehicle={vehicle}>
                        <DropdownMenuItem 
                          onSelect={(e: React.MouseEvent<HTMLDivElement>) => e.preventDefault()}
                          className="font-arabic"
                        >
                          <Edit className="w-4 h-4 ml-2" />
                          تعديل
                        </DropdownMenuItem>
                      </EditVehicleModal>
                      <DeleteConfirmModal
                        title="حذف المركبة"
                        description={`هل أنت متأكد من حذف ${vehicle.name}؟ سيتم حذف جميع البيانات والصور المرتبطة بها.`}
                      >
                        <DropdownMenuItem 
                          onSelect={(e: React.MouseEvent<HTMLDivElement>) => e.preventDefault()}
                          className="font-arabic text-red-600"
                        >
                          <Trash2 className="w-4 h-4 ml-2" />
                          حذف
                        </DropdownMenuItem>
                      </DeleteConfirmModal>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Vehicle Details */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground font-arabic">رقم اللوحة:</span>
                    <span className="font-medium">{vehicle.licensePlate}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground font-arabic">رقم الشاصي:</span>
                    <span className="font-medium text-xs">{vehicle.chassisNumber}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground font-arabic">اللون:</span>
                    <span className="font-medium">{vehicle.color}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground font-arabic">المسافة المقطوعة:</span>
                    <span className="font-medium">{vehicle.mileage}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground font-arabic">انتهاء الترخيص:</span>
                    <span className={`font-medium ${
                      new Date(vehicle.licenseExpiry) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                        ? "text-red-600"
                        : "text-green-600"
                    }`}>
                      {new Date(vehicle.licenseExpiry).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                </div>

                {/* Service Info */}
                <div className="bg-muted/30 rounded-lg p-4 mb-6">
                  <h4 className="font-arabic font-medium text-sm text-card-foreground mb-2">
                    معلومات الصيانة
                  </h4>
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div>
                      <span className="text-muted-foreground">آخر صيانة:</span>
                      <div className="font-medium">
                        {new Date(vehicle.lastService).toLocaleDateString('ar-SA')}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">الصيانة القادمة:</span>
                      <div className="font-medium">
                        {new Date(vehicle.nextService).toLocaleDateString('ar-SA')}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button 
                    className="flex-1 btn-premium text-white font-arabic" 
                    size="sm"
                    onClick={() => router.push(`/vehicles/${vehicle.id}`)}
                  >
                    <Eye className="w-4 h-4 ml-2" />
                    عرض التفاصيل
                  </Button>
                  <EditVehicleModal vehicle={vehicle}>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="px-3"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                  </EditVehicleModal>
                  <VehicleSettingsModal vehicleName={vehicle.name}>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="px-3"
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                  </VehicleSettingsModal>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredVehicles.length === 0 && (
          <Card className="premium-card p-12 text-center fade-in-up">
            <Car className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-almarai font-bold text-lg text-card-foreground mb-2">
              لا توجد مركبات
            </h3>
            <p className="text-muted-foreground font-arabic mb-6">
              {searchQuery 
                ? "لم يتم العثور على مركبات تطابق البحث" 
                : "ابدأ بإضافة مركبتك الأولى لتتبع صيانتها وخدماتها"
              }
            </p>
            <AddVehicleModal>
              <Button className="btn-premium text-white font-arabic gap-2">
                <Plus className="w-5 h-5" />
                إضافة مركبة جديدة
              </Button>
            </AddVehicleModal>
          </Card>
        )}
      </div>
    </div>
  );
}