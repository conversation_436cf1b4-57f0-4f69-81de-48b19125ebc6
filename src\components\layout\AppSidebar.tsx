import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  Car,
  Upload,
  CreditCard,
  FileText,
  Bell,
  Settings,
  LogOut,
  Wrench,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  useSidebar,
  SidebarRail,
} from "@/components/ui/sidebar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Customer menu items
const customerMenuItems = [
  { title: "الرئيسية", url: "/dashboard", icon: Home },
  { title: "مركباتي", url: "/vehicles", icon: Car },
  { title: "رفع الصور", url: "/upload", icon: Upload },
  { title: "الفواتير والدفعات", url: "/invoices", icon: CreditCard },
  { title: "التقارير", url: "/reports", icon: FileText },
  { title: "الإشعارات", url: "/notifications", icon: Bell },
  { title: "الكراجات", url: "/garages", icon: Wrench },
  { title: "الإعدادات", url: "/settings", icon: Settings },
];

// Garage owner menu items
const garageMenuItems = [
  { title: "لوحة التحكم", url: "/garage-dashboard", icon: Home },
  { title: "إدارة الطلبات", url: "/garage-dashboard/orders", icon: FileText },
  { title: "إدارة الخدمات", url: "/garage-dashboard/services", icon: Wrench },
  { title: "المركبات داخل الكراج", url: "/garage-dashboard/vehicles", icon: Car },
  { title: "العملاء والتقييمات", url: "/garage-dashboard/customers", icon: CreditCard },
  { title: "التقارير والإحصائيات", url: "/garage-dashboard/reports", icon: FileText },
  { title: "الإشعارات", url: "/garage-dashboard/notifications", icon: Bell },
  { title: "الإعدادات", url: "/garage-dashboard/settings", icon: Settings },
];

export function AppSidebar() {
  const { state, isMobile } = useSidebar();
  const location = useLocation();
  const isCollapsed = state === "collapsed";

  // Determine if we're in garage dashboard mode
  const isGarageMode = location.pathname.startsWith('/garage-dashboard');
  const menuItems = isGarageMode ? garageMenuItems : customerMenuItems;

  const isActive = (path: string) => location.pathname === path;

  return (
    <Sidebar
      side="right"
      className="border-l border-sidebar-border bg-sidebar"
      collapsible="offcanvas"
    >
      {/* Header */}
      <SidebarHeader className="p-4 md:p-6 border-b border-sidebar-border/50">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary-dark rounded-xl flex items-center justify-center">
            <Car className="w-5 h-5 text-primary-foreground" />
          </div>
          {(!isCollapsed || isMobile) && (
            <div className="flex-1 min-w-0">
              <h1 className="text-lg font-almarai font-bold text-sidebar-foreground truncate">
                خدمة السيارات
              </h1>
              <p className="text-sm text-sidebar-foreground/60">النظام المتطور</p>
            </div>
          )}
        </div>
      </SidebarHeader>

      {/* Content */}
      <SidebarContent className="px-2 md:px-3 py-4">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-2">
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive(item.url)}
                    className={`
                      w-full h-12 md:h-14 rounded-xl transition-all duration-300 
                      ${
                        isActive(item.url)
                          ? "bg-gradient-to-r from-primary/20 to-primary/10 text-primary border border-primary/20 shadow-md scale-[1.02]"
                          : "hover:bg-sidebar-accent/70 text-sidebar-foreground/70 hover:text-sidebar-foreground hover:scale-[1.01]"
                      }
                      touch-target
                    `}
                    tooltip={isCollapsed && !isMobile ? item.title : undefined}
                  >
                    <NavLink
                      to={item.url}
                      className="flex items-center gap-3 w-full justify-start px-4"
                    >
                      <item.icon
                        className={`w-5 h-5 flex-shrink-0 ${
                          isActive(item.url) ? "text-primary" : ""
                        }`}
                      />
                      {(!isCollapsed || isMobile) && (
                        <span className="font-arabic font-medium text-sm truncate">
                          {item.title}
                        </span>
                      )}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      {/* Footer */}
      <SidebarFooter className="p-3 md:p-4 border-t border-sidebar-border/50">
        <div className="flex items-center gap-3 mb-4">
          <Avatar className="w-10 h-10 ring-2 ring-primary/20">
            <AvatarImage src="/api/placeholder/40/40" />
            <AvatarFallback className="bg-gradient-to-br from-primary to-primary-dark text-primary-foreground font-arabic font-bold">
              أح
            </AvatarFallback>
          </Avatar>
          {(!isCollapsed || isMobile) && (
            <div className="flex-1 min-w-0">
              <p className="font-arabic font-medium text-sm text-sidebar-foreground truncate">
                أحمد محمد
              </p>
              <p className="text-xs text-sidebar-foreground/60 truncate">
                <EMAIL>
              </p>
            </div>
          )}
        </div>

        <SidebarMenuItem>
          <SidebarMenuButton
            onClick={() => alert('تسجيل الخروج')}
            className="w-full h-11 rounded-xl text-destructive hover:bg-destructive/10 hover:text-destructive justify-start px-4"
            tooltip={isCollapsed && !isMobile ? "تسجيل الخروج" : undefined}
          >
            <LogOut className="w-5 h-5 flex-shrink-0" />
            {(!isCollapsed || isMobile) && (
              <span className="font-arabic font-medium text-sm">
                تسجيل الخروج
              </span>
            )}
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}