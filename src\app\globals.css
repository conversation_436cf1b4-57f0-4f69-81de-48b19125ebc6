@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&family=Almarai:wght@300;400;700;800&display=swap');

@import "tailwindcss/preflight";
@import "tailwindcss/components";
@import "tailwindcss/utilities";



@layer base {
  :root {
    /* Premium Blue & Black Gradient Palette */
    --background: 220 20% 97%;
    --foreground: 210 15% 10%;

    --card: 0 0% 100%;
    --card-foreground: 210 15% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 15% 10%;

    /* Premium Blue Primary */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 217 91% 75%;
    --primary-dark: 217 91% 45%;

    /* Premium Secondary */
    --secondary: 210 20% 95%;
    --secondary-foreground: 210 15% 15%;

    /* Muted colors */
    --muted: 210 15% 93%;
    --muted-foreground: 210 10% 45%;

    /* Accent colors */
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 100%;

    /* Status colors */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Borders and inputs */
    --border: 210 20% 85%;
    --input: 210 20% 90%;
    --ring: 217 91% 60%;

    /* Premium Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(217 91% 45%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(210 20% 95%) 0%, hsl(210 15% 85%) 100%);
    --gradient-dark: linear-gradient(135deg, hsl(210 15% 10%) 0%, hsl(210 20% 5%) 100%);
    --gradient-glass: linear-gradient(135deg, hsla(217 91% 60% / 0.1) 0%, hsla(217 91% 45% / 0.05) 100%);

    /* Premium Shadows */
    --shadow-soft: 0 2px 10px -3px hsl(217 91% 60% / 0.1);
    --shadow-medium: 0 8px 30px -12px hsl(217 91% 60% / 0.25);
    --shadow-strong: 0 20px 40px -12px hsl(217 91% 60% / 0.4);
    --shadow-glow: 0 0 0 1px hsl(217 91% 60% / 0.1), 0 0 20px hsl(217 91% 60% / 0.3);

    --radius: 0.75rem;

    /* Sidebar specific */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 210 15% 10%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 20% 95%;
    --sidebar-accent-foreground: 210 15% 15%;
    --sidebar-border: 210 20% 85%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    /* Dark mode - Premium black with blue accents */
    --background: 210 20% 3%;
    --foreground: 210 15% 95%;

    --card: 210 15% 5%;
    --card-foreground: 210 15% 95%;

    --popover: 210 15% 5%;
    --popover-foreground: 210 15% 95%;

    /* Primary stays vibrant in dark */
    --primary: 217 91% 65%;
    --primary-foreground: 210 20% 3%;
    --primary-light: 217 91% 75%;
    --primary-dark: 217 91% 55%;

    --secondary: 210 15% 8%;
    --secondary-foreground: 210 15% 85%;

    --muted: 210 15% 7%;
    --muted-foreground: 210 10% 55%;

    --accent: 217 91% 65%;
    --accent-foreground: 210 20% 3%;

    --success: 142 71% 55%;
    --warning: 38 92% 60%;
    --destructive: 0 84% 65%;

    --border: 210 15% 15%;
    --input: 210 15% 10%;
    --ring: 217 91% 65%;

    /* Dark gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 65%) 0%, hsl(217 91% 50%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(210 15% 8%) 0%, hsl(210 15% 5%) 100%);
    --gradient-dark: linear-gradient(135deg, hsl(210 20% 3%) 0%, hsl(210 15% 1%) 100%);
    --gradient-glass: linear-gradient(135deg, hsla(217 91% 65% / 0.15) 0%, hsla(217 91% 50% / 0.08) 100%);

    /* Dark shadows with blue glow */
    --shadow-soft: 0 2px 10px -3px hsl(217 91% 65% / 0.2);
    --shadow-medium: 0 8px 30px -12px hsl(217 91% 65% / 0.3);
    --shadow-strong: 0 20px 40px -12px hsl(217 91% 65% / 0.5);
    --shadow-glow: 0 0 0 1px hsl(217 91% 65% / 0.2), 0 0 30px hsl(217 91% 65% / 0.4);

    --sidebar-background: 210 15% 4%;
    --sidebar-foreground: 210 15% 90%;
    --sidebar-primary: 217 91% 65%;
    --sidebar-primary-foreground: 210 20% 3%;
    --sidebar-accent: 210 15% 8%;
    --sidebar-accent-foreground: 210 15% 85%;
    --sidebar-border: 210 15% 12%;
    --sidebar-ring: 217 91% 65%;
  }

  /* RTL Support */
  html {
    direction: rtl;
  }

  body {
    @apply bg-background text-foreground font-arabic;
    font-feature-settings: "liga" 1, "kern" 1;
    text-rendering: optimizeLegibility;
  }

  /* Premium scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted/30;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary/40 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/60;
  }
}

@layer components {

  /* Premium Glassmorphism Effect */
  .glass-effect {
    background: var(--gradient-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid hsl(var(--border) / 0.2);
  }

  /* Premium Card Styles */
  .premium-card {
    @apply bg-card border border-border/50 rounded-xl;
    box-shadow: var(--shadow-soft);
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.95) 100%);
  }

  .premium-card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Premium Button Variants */
  .btn-premium {
    @apply relative overflow-hidden;
    background: var(--gradient-primary);
    box-shadow: var(--shadow-soft);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-premium:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
  }

  /* Mobile responsive utilities */
  @media (max-width: 768px) {
    .premium-card {
      @apply rounded-lg border-0 shadow-lg;
      background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.98) 100%);
    }

    .glass-effect {
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
    }

    /* Mobile app-like layout */
    .mobile-app-layout {
      @apply min-h-screen bg-gradient-to-br from-background via-background to-muted/10;
    }

    /* Mobile sidebar improvements */
    .sidebar-mobile {
      @apply w-full max-w-xs shadow-2xl;
    }

    /* Mobile typography */
    .mobile-text-lg {
      @apply text-lg leading-tight;
    }

    .mobile-text-base {
      @apply text-base leading-snug;
    }

    /* Mobile card spacing */
    .mobile-card-spacing {
      @apply p-4 gap-4;
    }

    /* Mobile button improvements */
    .mobile-btn {
      @apply h-12 px-6 rounded-xl font-medium;
    }

    /* Safe area for mobile devices */
    .mobile-safe-area {
      padding-bottom: env(safe-area-inset-bottom);
      padding-top: env(safe-area-inset-top);
    }

    /* Mobile App Layout */
    .mobile-app-layout {
      @apply antialiased text-foreground w-full;
    }

    /* Mobile Sidebar */
    .sidebar-mobile {
      @apply md:relative md:translate-x-0;
    }

    .sidebar-mobile {
      @apply fixed inset-y-0 z-50 w-80 transform transition-transform duration-300 ease-in-out;
    }

    .sidebar-mobile[data-state="collapsed"] {
      @apply -translate-x-full;
    }

    .sidebar-mobile[data-state="expanded"] {
      @apply translate-x-0;
    }
  }

  /* Dark mode improvements */
  .dark {

    /* Better contrast for cards in dark mode */
    .premium-card {
      background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.98) 100%);
      border-color: hsl(var(--border) / 0.3);
    }

    /* Glassmorphism improvements for dark mode */
    .glass-effect {
      background: linear-gradient(135deg, hsla(217 91% 65% / 0.08) 0%, hsla(217 91% 50% / 0.04) 100%);
      border-color: hsl(var(--border) / 0.1);
    }
  }

  /* Touch improvements for mobile */
  @media (hover: none) and (pointer: coarse) {
    .hover-scale {
      @apply transform-none;
    }

    .premium-card:hover {
      transform: none;
    }

    /* Larger touch targets on mobile */
    .touch-target {
      @apply min-h-[44px] min-w-[44px];
    }
  }

  /* Premium Input Styles */
  .input-premium {
    @apply bg-card border border-border/50 rounded-lg px-4 py-3;
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .input-premium:focus {
    @apply border-primary/50 ring-2 ring-primary/20;
    box-shadow: var(--shadow-glow);
  }

  /* Arabic Typography Classes */
  .heading-arabic {
    @apply font-almarai font-bold tracking-wide;
    text-shadow: 0 1px 2px hsl(var(--foreground) / 0.1);
  }

  .text-arabic {
    @apply font-arabic leading-relaxed;
  }

  /* Premium Animations */
  .fade-in-up {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .scale-in {
    animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .slide-in-right {
    animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}


#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}