import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { 
  Search, 
  Filter, 
  MapPin, 
  Star, 
  Phone, 
  Clock,
  Wrench,
  Heart,
  Grid,
  List
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

const garages = [
  {
    id: 1,
    name: "كراج الخليج المتطور",
    rating: 4.8,
    reviews: 245,
    location: "الرياض، حي النخيل",
    distance: "2.5 كم",
    services: ["صيانة دورية", "إصلاح محركات", "كهرباء السيارات"],
    priceRange: "متوسط",
    isOpen: true,
    openHours: "8:00 ص - 10:00 م",
    phone: "+966 11 234 5678",
    image: "/api/placeholder/300/200",
    specialization: "جميع أنواع السيارات",
    workTime: "24 ساعة"
  },
  {
    id: 2,
    name: "مركز الصيانة السريعة",
    rating: 4.6,
    reviews: 189,
    location: "الرياض، حي الملز",
    distance: "4.1 كم",
    services: ["تغيير زيت", "فحص شامل", "إطارات"],
    priceRange: "اقتصادي",
    isOpen: true,
    openHours: "7:00 ص - 11:00 م",
    phone: "+966 11 345 6789",
    image: "/api/placeholder/300/200",
    specialization: "سيارات يابانية",
    workTime: "16 ساعة"
  },
  {
    id: 3,
    name: "ورشة النجمة الذهبية",
    rating: 4.9,
    reviews: 312,
    location: "الرياض، حي العليا",
    distance: "6.8 كم",
    services: ["إصلاح هيكل", "دهان", "تجليد مقاعد"],
    priceRange: "مرتفع",
    isOpen: false,
    openHours: "8:00 ص - 6:00 م",
    phone: "+966 11 456 7890",
    image: "/api/placeholder/300/200",
    specialization: "سيارات فاخرة",
    workTime: "10 ساعات"
  },
  {
    id: 4,
    name: "مركز الشرق للصيانة",
    rating: 4.4,
    reviews: 156,
    location: "الرياض، حي السلام",
    distance: "8.2 كم",
    services: ["تكييف", "فرامل", "نقل حركة"],
    priceRange: "متوسط",
    isOpen: true,
    openHours: "9:00 ص - 8:00 م",
    phone: "+966 11 567 8901",
    image: "/api/placeholder/300/200",
    specialization: "سيارات أمريكية",
    workTime: "11 ساعة"
  },
];

const priceRangeColors = {
  "اقتصادي": "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400",
  "متوسط": "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400",
  "مرتفع": "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400",
};

export default function Garages() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);
  const { toast } = useToast();

  const filteredGarages = garages.filter(garage =>
    garage.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    garage.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
    garage.services.some(service => 
      service.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  return (
    <div className="min-h-screen bg-background p-4 md:p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="fade-in-up">
          <h1 className="text-3xl font-almarai font-bold text-foreground mb-2">
            الكراجات والورش
          </h1>
          <p className="text-muted-foreground font-arabic">
            ابحث عن أفضل الكراجات والورش في منطقتك
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="premium-card p-6 fade-in-up" style={{ animationDelay: "0.1s" }}>
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <Input
                placeholder="ابحث عن كراج أو خدمة..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-premium pr-12 h-12 font-arabic"
              />
            </div>

            {/* Filter and View Controls */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="h-12 px-6"
              >
                <Filter className="w-5 h-5 ml-2" />
                تصفية
              </Button>

              <div className="flex border border-border rounded-lg p-1">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="h-10 px-3"
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="h-10 px-3"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Filters */}
          <div className="flex flex-wrap gap-2 mt-4">
            <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
              الكل
            </Badge>
            <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
              مفتوح الآن
            </Badge>
            <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
              تقييم عالي
            </Badge>
            <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
              قريب مني
            </Badge>
            <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
              أسعار اقتصادية
            </Badge>
          </div>
        </Card>

        {/* Results Header */}
        <div className="flex items-center justify-between fade-in-up" style={{ animationDelay: "0.2s" }}>
          <p className="text-muted-foreground font-arabic">
            عرض {filteredGarages.length} من {garages.length} كراج
          </p>
          <select className="bg-card border border-border rounded-lg px-4 py-2 text-sm font-arabic">
            <option>الأقرب للموقع</option>
            <option>أعلى تقييم</option>
            <option>الأسعار الأقل</option>
            <option>الأكثر شعبية</option>
          </select>
        </div>

        {/* Garages Grid */}
        <div className={`
          grid gap-6
          ${viewMode === "grid" 
            ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" 
            : "grid-cols-1"
          }
        `}>
          {filteredGarages.map((garage, index) => (
            <Card
              key={garage.id}
              className={`
                premium-card overflow-hidden group cursor-pointer
                hover:shadow-xl transition-all duration-300
                ${viewMode === "list" ? "flex flex-col md:flex-row" : ""}
              `}
              style={{ animationDelay: `${(index + 3) * 0.1}s` }}
            >
              {/* Image */}
              <div className={`
                relative overflow-hidden
                ${viewMode === "list" ? "md:w-64 h-48 md:h-auto" : "h-48"}
              `}>
                <img
                  src={garage.image}
                  alt={garage.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-3 left-3 flex gap-2">
                  <Badge 
                    className={garage.isOpen 
                      ? "bg-green-500 text-white" 
                      : "bg-red-500 text-white"
                    }
                  >
                    {garage.isOpen ? "مفتوح" : "مغلق"}
                  </Badge>
                  <Badge 
                    className={priceRangeColors[garage.priceRange as keyof typeof priceRangeColors]}
                  >
                    {garage.priceRange}
                  </Badge>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toast({
                    title: "تمت إضافة الكراج للمفضلة",
                    description: `تم إضافة ${garage.name} إلى قائمة المفضلة`,
                  })}
                  className="absolute top-3 right-3 w-8 h-8 p-0 bg-white/80 hover:bg-white"
                >
                  <Heart className="w-4 h-4" />
                </Button>
              </div>

              {/* Content */}
              <div className="p-6 flex-1">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-almarai font-bold text-lg text-card-foreground mb-1 truncate">
                      {garage.name}
                    </h3>
                    <p className="text-sm text-muted-foreground font-arabic">
                      {garage.specialization}
                    </p>
                  </div>
                  <div className="flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-lg">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-bold text-yellow-700 dark:text-yellow-400">
                      {garage.rating}
                    </span>
                  </div>
                </div>

                {/* Location and Distance */}
                <div className="flex items-center gap-2 mb-3">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground font-arabic">
                    {garage.location}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {garage.distance}
                  </Badge>
                </div>

                {/* Services */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {garage.services.slice(0, 3).map((service) => (
                    <Badge key={service} variant="secondary" className="text-xs">
                      {service}
                    </Badge>
                  ))}
                  {garage.services.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{garage.services.length - 3}
                    </Badge>
                  )}
                </div>

                {/* Details */}
                <div className="space-y-2 mb-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    <span className="font-arabic">{garage.openHours}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    <span>{garage.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Wrench className="w-4 h-4" />
                    <span className="font-arabic">ساعات العمل: {garage.workTime}</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-3">
                  <Button 
                    className="flex-1 btn-premium text-white font-arabic"
                    onClick={() => navigate(`/garage/${garage.id}`)}
                  >
                    عرض التفاصيل
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="px-3"
                    onClick={() => window.open(`tel:${garage.phone}`)}
                  >
                    <Phone className="w-4 h-4" />
                  </Button>
                </div>

                {/* Reviews */}
                <div className="mt-3 pt-3 border-t border-border/50">
                  <p className="text-xs text-muted-foreground font-arabic">
                    {garage.reviews} تقييم من العملاء
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center fade-in-up" style={{ animationDelay: "0.8s" }}>
          <Button 
            variant="outline" 
            size="lg" 
            className="font-arabic"
            onClick={() => toast({
              title: "تحميل المزيد",
              description: "سيتم تحميل المزيد من الكراجات قريباً",
            })}
          >
            تحميل المزيد من الكراجات
          </Button>
        </div>
      </div>
    </div>
  );
}