import Dashboard from "@/pages/Dashboard";

// This is a server component that will be rendered on the server
export default async function CustomerDashboardPage() {
  // Simulate data fetching that would happen on the server
  const dashboardData = await getDashboardData();

  return <Dashboard initialData={dashboardData} />;
}

// Mock server-side data fetching function
async function getDashboardData() {
  // In a real app, this would fetch from your database or API
  return {
    user: {
      name: "أحمد محمد",
      email: "<EMAIL>"
    },
    stats: {
      totalVehicles: 3,
      pendingServices: 2,
      completedServices: 15,
      totalSpent: 2500
    },
    recentActivity: [
      {
        id: 1,
        type: "service_completed",
        message: "تم إكمال صيانة دورية للمركبة تويوتا كامري",
        date: new Date().toISOString(),
        status: "completed"
      },
      {
        id: 2,
        type: "appointment_scheduled",
        message: "تم حجز موعد صيانة للمركبة هوندا أكورد",
        date: new Date(Date.now() - 86400000).toISOString(),
        status: "pending"
      }
    ]
  };
}
