import { NavLink, useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  Users,
  Building2,
  ShoppingBag,
  DollarSign,
  Star,
  Bell,
  Headphones,
  Settings,
  LogOut,
  Crown,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  useSidebar,
  SidebarRail,
} from "@/components/ui/sidebar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

// Admin menu items
const adminMenuItems = [
  { title: "الرئيسية", url: "/admin", icon: LayoutDashboard, badge: null },
  { title: "إدارة المستخدمين", url: "/admin/users", icon: Users, badge: "12" },
  { title: "إدارة الكراجات", url: "/admin/garages", icon: Building2, badge: "3" },
  { title: "نظرة عامة على الطلبات", url: "/admin/orders", icon: ShoppingBag, badge: "45" },
  { title: "التقارير المالية", url: "/admin/reports", icon: DollarSign, badge: null },
  { title: "المراجعات والتقييمات", url: "/admin/reviews", icon: Star, badge: "8" },
  { title: "الإشعارات والرسائل", url: "/admin/notifications", icon: Bell, badge: "2" },
  { title: "الدعم الفني", url: "/admin/support", icon: Headphones, badge: "5" },
  { title: "الإعدادات العامة", url: "/admin/settings", icon: Settings, badge: null },
];

export function AdminSidebar() {
  const { state, isMobile } = useSidebar();
  const location = useLocation();
  const isCollapsed = state === "collapsed";

  const isActive = (path: string) => {
    if (path === "/admin") {
      return location.pathname === "/admin";
    }
    return location.pathname.startsWith(path);
  };

  return (
    <Sidebar
      side="right"
      className="border-l border-border/50 bg-gradient-to-b from-card via-card/95 to-muted/20 backdrop-blur-sm"
      collapsible="offcanvas"
    >
      {/* Header */}
      <SidebarHeader className="p-4 md:p-6 border-b border-border/30">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 rounded-2xl flex items-center justify-center shadow-lg ring-2 ring-purple-600/20">
            <Crown className="w-6 h-6 text-white" />
          </div>
          {(!isCollapsed || isMobile) && (
            <div className="flex-1 min-w-0">
              <h1 className="text-xl font-bold text-foreground truncate bg-gradient-to-r from-purple-600 to-purple-800 bg-clip-text text-transparent">
                لوحة الإدارة
              </h1>
              <p className="text-sm text-muted-foreground">نظام إدارة المنصة</p>
            </div>
          )}
        </div>
      </SidebarHeader>

      {/* Content */}
      <SidebarContent className="px-2 md:px-3 py-4">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {adminMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive(item.url)}
                    className={`
                      w-full h-14 rounded-2xl transition-all duration-300 group relative overflow-hidden
                      ${
                        isActive(item.url)
                          ? "bg-gradient-to-r from-purple-600/20 via-purple-500/15 to-purple-400/10 text-purple-700 dark:text-purple-300 border border-purple-500/30 shadow-lg shadow-purple-500/20 scale-[1.02]"
                          : "hover:bg-gradient-to-r hover:from-muted/70 hover:to-muted/50 text-muted-foreground hover:text-foreground hover:scale-[1.01] hover:shadow-md"
                      }
                      touch-target
                    `}
                    tooltip={isCollapsed && !isMobile ? item.title : undefined}
                  >
                    <NavLink
                      to={item.url}
                      end={item.url === "/admin"}
                      className="flex items-center gap-4 w-full justify-start px-4 relative"
                    >
                      <item.icon
                        className={`w-6 h-6 flex-shrink-0 transition-all duration-300 ${
                          isActive(item.url) 
                            ? "text-purple-600 dark:text-purple-400 scale-110" 
                            : "group-hover:scale-105"
                        }`}
                      />
                      {(!isCollapsed || isMobile) && (
                        <>
                          <span className="font-medium text-sm truncate">
                            {item.title}
                          </span>
                          {item.badge && (
                            <Badge 
                              variant="secondary" 
                              className={`mr-auto text-xs px-2 py-1 rounded-full ${
                                isActive(item.url)
                                  ? "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300"
                                  : "bg-muted text-muted-foreground"
                              }`}
                            >
                              {item.badge}
                            </Badge>
                          )}
                        </>
                      )}
                      {isCollapsed && !isMobile && item.badge && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full border-2 border-background"></div>
                      )}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      {/* Footer */}
      <SidebarFooter className="p-3 md:p-4 border-t border-border/30">
        <div className="flex items-center gap-3 mb-4 p-3 rounded-xl bg-gradient-to-r from-muted/50 to-muted/30 border border-border/20">
          <Avatar className="w-12 h-12 ring-2 ring-purple-500/30 shadow-lg">
            <AvatarImage src="/api/placeholder/48/48" />
            <AvatarFallback className="bg-gradient-to-br from-purple-600 to-purple-800 text-white font-bold text-lg">
              أ.إ
            </AvatarFallback>
          </Avatar>
          {(!isCollapsed || isMobile) && (
            <div className="flex-1 min-w-0">
              <p className="font-semibold text-sm text-foreground truncate">
                أحمد الإداري
              </p>
              <p className="text-xs text-muted-foreground truncate">
                مدير النظام
              </p>
            </div>
          )}
        </div>

        <SidebarMenuItem>
          <SidebarMenuButton
            onClick={() => alert('تسجيل الخروج')}
            className="w-full h-12 rounded-xl text-destructive hover:bg-destructive/10 hover:text-destructive justify-start px-4 transition-all duration-300 hover:scale-105"
            tooltip={isCollapsed && !isMobile ? "تسجيل الخروج" : undefined}
          >
            <LogOut className="w-5 h-5 flex-shrink-0" />
            {(!isCollapsed || isMobile) && (
              <span className="font-medium text-sm">
                تسجيل الخروج
              </span>
            )}
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}